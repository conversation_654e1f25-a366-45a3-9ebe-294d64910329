{"name": "viraldeals-frontend", "private": true, "version": "1.0.0", "description": "Frontend for ViralDeals.online - Indian E-commerce Platform", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "vercel-build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "axios": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-image-webp": "^0.8.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.3", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.3", "vitest": "^3.2.4"}}