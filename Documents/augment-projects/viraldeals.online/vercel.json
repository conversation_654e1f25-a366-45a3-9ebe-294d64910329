{"version": 2, "buildCommand": "npm run vercel-build", "outputDirectory": "frontend/dist", "installCommand": "npm install --prefix api && npm install --prefix backend && npm install --prefix frontend", "rewrites": [{"source": "/api/(.*)", "destination": "/api"}, {"source": "/uploads/(.*)", "destination": "/api"}, {"source": "/(.*)", "destination": "/index.html"}], "functions": {"api/index.js": {"maxDuration": 30}}}