{"version": 2, "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}, {"src": "backend/server.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/backend/server.js"}, {"src": "/uploads/(.*)", "dest": "/backend/server.js"}, {"src": "/(.*)", "dest": "/frontend/dist/$1"}], "env": {"NODE_ENV": "production"}, "functions": {"backend/server.js": {"maxDuration": 30}}}